import { createFileRoute } from "@tanstack/react-router";
import JokeForm from "~/components/JokeForm";
import { getJokeById } from "~/serverFn/jokesServerFn";

export const Route = createFileRoute("/jokes/$id")({
	loader: async ({ params }) => {
		const id = params.id;
		return await getJokeById({ data: id });
	},
	component: RouteComponent,
});

function RouteComponent() {
	const joke = Route.useLoaderData();

	if (!joke) {
		return <p>Joke not found!</p>;
	}

	return <JokeForm key={joke.id} joke={joke} />;
}
